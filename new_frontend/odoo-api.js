/**
 * Odoo API Client for Web Frontend
 * Handles all communication with Odoo XML-RPC API
 */

class OdooAPI {
    constructor() {
        this.url = '';
        this.db = '';
        this.username = '';
        this.password = '';
        this.uid = null;
        this.isConnected = false;
    }

    /**
     * Connect to Odoo and authenticate
     */
    async connect(url, db, username, password) {
        try {
            this.url = url;
            this.db = db;
            this.username = username;
            this.password = password;

            console.log('Connecting to Odoo...', { url, db, username });

            // Make request to our proxy server
            const response = await fetch('/api/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url, db, username, password })
            });

            const result = await response.json();

            if (result.success) {
                this.uid = result.uid;
                this.isConnected = true;
                console.log('Connected successfully, UID:', this.uid);
                return { success: true, uid: this.uid };
            } else {
                this.isConnected = false;
                console.error('Connection failed:', result.error);
                return { success: false, error: result.error };
            }

        } catch (error) {
            console.error('Connection failed:', error);
            this.isConnected = false;
            return { success: false, error: error.message };
        }
    }

    /**
     * Check if connected to Odoo
     */
    isAuthenticated() {
        return this.isConnected && this.uid;
    }

    /**
     * Make a call to Odoo API
     */
    async call(model, method, args = [], kwargs = {}) {
        if (!this.isAuthenticated()) {
            throw new Error('Not authenticated');
        }

        try {
            console.log('API Call:', { model, method, args, kwargs });

            // Make request to our proxy server
            const response = await fetch('/api/call', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ model, method, args, kwargs })
            });

            const result = await response.json();

            if (result.success) {
                return result.result;
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }



    /**
     * Search for records
     */
    async search(model, domain = [], options = {}) {
        return await this.call(model, 'search', [domain], options);
    }

    /**
     * Read records
     */
    async read(model, ids, fields = []) {
        const options = fields.length > 0 ? { fields } : {};
        return await this.call(model, 'read', [ids], options);
    }

    /**
     * Search and read records in one call
     */
    async searchRead(model, domain = [], fields = [], options = {}) {
        const ids = await this.search(model, domain, options);
        if (ids.length === 0) return [];
        return await this.read(model, ids, fields);
    }

    /**
     * Create a new record
     */
    async create(model, values) {
        return await this.call(model, 'create', [values]);
    }

    /**
     * Update existing records
     */
    async write(model, ids, values) {
        return await this.call(model, 'write', [ids, values]);
    }

    /**
     * Delete records
     */
    async unlink(model, ids) {
        return await this.call(model, 'unlink', [ids]);
    }

    /**
     * Execute a method on a model
     */
    async execute(model, method, ids, ...args) {
        return await this.call(model, method, [ids, ...args]);
    }
}

// Create global instance
window.odooAPI = new OdooAPI();
