<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Odoo Attendance Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-clock"></i> Odoo Attendance Dashboard</h1>
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator"></span>
                <span class="status-text">Disconnected</span>
            </div>
        </header>

        <!-- Login Section -->
        <div class="login-section" id="loginSection">
            <div class="login-card">
                <h2><i class="fas fa-sign-in-alt"></i> Connect to Odoo</h2>
                <form id="loginForm">
                    <div class="form-group">
                        <label for="odooUrl">Odoo URL:</label>
                        <input type="text" id="odooUrl" value="http://localhost:10017" required>
                    </div>
                    <div class="form-group">
                        <label for="database">Database:</label>
                        <input type="text" id="database" value="attendance_test" required>
                    </div>
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" value="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" value="12345" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plug"></i> Connect
                    </button>
                </form>
            </div>
        </div>

        <!-- Main Dashboard -->
        <main id="dashboard" style="display: none;">
            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalEmployees">-</div>
                        <div class="stat-label">Total Employees</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-user-check"></i></div>
                    <div class="stat-content">
                        <div class="stat-value" id="presentToday">-</div>
                        <div class="stat-label">Present Today</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-clock"></i></div>
                    <div class="stat-content">
                        <div class="stat-value" id="totalHoursToday">-</div>
                        <div class="stat-label">Hours Today</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                    <div class="stat-content">
                        <div class="stat-value" id="attendanceRecords">-</div>
                        <div class="stat-label">Records Today</div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="actions">
                <button class="btn btn-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button class="btn btn-success" onclick="showCreateEmployee()">
                    <i class="fas fa-user-plus"></i> Add Employee
                </button>
                <button class="btn btn-info" onclick="showAttendanceForm()">
                    <i class="fas fa-clock"></i> Record Attendance
                </button>
                <button class="btn btn-warning" onclick="exportAttendance()">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>

            <!-- Main Content Grid -->
            <div class="dashboard-grid">
                <!-- Employees Section -->
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-users"></i> Employees</h2>
                        <div class="card-actions">
                            <input type="text" id="employeeSearch" placeholder="Search employees..." onkeyup="filterEmployees()">
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="employee-list" id="employeeList">
                            <div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading employees...</div>
                        </div>
                    </div>
                </div>

                <!-- Recent Attendance Section -->
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-history"></i> Recent Attendance</h2>
                        <div class="card-actions">
                            <select id="attendanceFilter" onchange="filterAttendance()">
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="attendance-list" id="attendanceList">
                            <div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading attendance...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employee Details Modal -->
            <div class="modal" id="employeeModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="modalTitle">Employee Details</h3>
                        <span class="close" onclick="closeModal('employeeModal')">&times;</span>
                    </div>
                    <div class="modal-body" id="modalBody">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Create Employee Modal -->
            <div class="modal" id="createEmployeeModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Add New Employee</h3>
                        <span class="close" onclick="closeModal('createEmployeeModal')">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="createEmployeeForm">
                            <div class="form-group">
                                <label for="empName">Name:</label>
                                <input type="text" id="empName" required>
                            </div>
                            <div class="form-group">
                                <label for="empBarcode">Barcode:</label>
                                <input type="text" id="empBarcode">
                            </div>
                            <div class="form-group">
                                <label for="empEmail">Email:</label>
                                <input type="email" id="empEmail">
                            </div>
                            <div class="form-group">
                                <label for="empPhone">Phone:</label>
                                <input type="tel" id="empPhone">
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Employee
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeModal('createEmployeeModal')">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Attendance Form Modal -->
            <div class="modal" id="attendanceModal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Record Attendance</h3>
                        <span class="close" onclick="closeModal('attendanceModal')">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="attendanceForm">
                            <div class="form-group">
                                <label for="attEmployee">Employee:</label>
                                <select id="attEmployee" required>
                                    <option value="">Select Employee</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="attType">Action:</label>
                                <select id="attType" required>
                                    <option value="checkin">Check In</option>
                                    <option value="checkout">Check Out</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="attDateTime">Date & Time:</label>
                                <input type="datetime-local" id="attDateTime" required>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-clock"></i> Record Attendance
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeModal('attendanceModal')">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

        <!-- Toast Notifications -->
        <div id="toast" class="toast"></div>
    </div>

    <script src="odoo-api.js"></script>
    <script src="app.js"></script>
</body>
</html>
